import subprocess
import time
import ctypes
import sys
import keyboard
from datetime import datetime
 
def run_as_admin():
    """
    以管理模式运行Python文件
    """
    try:
        is_admin = bool(ctypes.windll.shell32.IsUserAnAdmin())
    except:
        is_admin = False
 
    if not is_admin:
        ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, __file__, None, 1)
        exit()
 
 
def get_sessions():
    """
    获取当前Windows操作系统的全部会话信息
    :return: [[session_name, session_id, session_status], ...], 如下所示：
            [
                ['services', '0', '断开'],
                ['>console', '1', '运行中'],
                ['rdp-tcp', '65536', '侦听']
            ]
    """
 
    # 创建子进程，执行“query session”命令，并捕获输出的内容
    process = subprocess.run(['query', 'session'], capture_output=True)
 
    # 获取标准输出原始数据，解码，并以每行数据转换成列表
    # 原始数据如下：
    #  会话名            用户名                   ID  状态    类型        设备
    #  services                                    0  断开
    # >console           Frank                     1  运行中
    #  rdp-tcp                                 65536  侦听
    # 转换后的数据:
    # [
    #     ['会话名', '用户名', 'ID', '状态', '类型'],
    #     ['services', '0', '断开'],
    #     ['>console', 'Frank', '1', '运行中'],
    #     ['rdp-tcp', '65536', '侦听'],
    #     []
    # ]
    original_data = process.stdout.decode('ansi').split('\r\n')
    sessions = []
    for x in original_data:
        temp = []
        x_list = x.split(' ')
        empty_quantity = x_list.count('')
        for number in range(empty_quantity):
            if x_list[number] != '':
                temp.append(x_list[number])
        sessions.append(temp)
 
    # 去头 => ['会话名', '用户名', 'ID', '状态', '类型']，去尾 => []
    sessions.pop(0)
    sessions.pop(-1)
 
    # 再加工，仅仅保留 [session_name, session_id, session_status]
    for session in sessions:
        if len(session) == 4:
            session.pop(1)
 
    # 返回经过加工处理的会话列表数据
    return sessions
 
 
def get_current_session():
    """
    获取当前正在运行中的session信息
    :return: [session_name, session_id, session_status]
    """
    # 获取全部会话信息
    sessions = get_sessions()

    # 遍历获取会话名中带'>'的会话，
    for session in sessions:
        if session[0][0] == '>':
            return session


def is_remote_connected():
    """
    检测是否有远程连接
    :return: True表示有远程连接，False表示无远程连接
    """
    session_name, session_id, session_status = get_current_session()
    # 如果session_name不是单独的'>'，说明有远程连接
    return session_name != '>'


def send_hotkey(hotkey, repeat_count=3):
    """
    发送快捷键
    :param hotkey: 快捷键组合，如'ctrl+alt+h'
    :param repeat_count: 重复发送次数，默认3次
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    success_count = 0

    for i in range(repeat_count):
        try:
            keyboard.send(hotkey)
            success_count += 1
            print(f"[{timestamp}] 第{i+1}次发送快捷键: {hotkey}")
            # 每次发送之间稍微延迟，确保系统能够处理
            time.sleep(1)
        except Exception as e:
            print(f"[{timestamp}] 第{i+1}次发送快捷键失败: {e}")

    print(f"[{timestamp}] 快捷键 {hotkey} 发送完成，成功 {success_count}/{repeat_count} 次")
 
 
if __name__ == '__main__':

    # 以管理员模式运行当前程序
    run_as_admin()

    # 初始化远程连接状态为None，只有检测到状态变化时才触发
    previous_remote_status = None
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] 程序启动，等待检测状态变化...")

    # 监测远程会话是否已退出，若退出则将会话切换成本地会话。
    # 远程桌面未关闭前，session_name显示如“>rdp-tcp#2”，
    # 远程桌面退出后，session_name显示成'>'，
    while True:
        # 获取当前session信息
        session_name, session_id, session_status = get_current_session()

        # 检测当前远程连接状态
        current_remote_status = is_remote_connected()

        # 判断远程桌面是否已断开，断开就切换成本地会话
        if session_name == '>':
            subprocess.run(['tscon', session_id, '/dest:console'])

        # 检测远程连接状态变化并发送相应快捷键
        if previous_remote_status is not None and current_remote_status != previous_remote_status:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if current_remote_status:
                # 检测到远程连接建立，发送快捷键
                print(f"[{timestamp}] 检测到远程连接建立")
                send_hotkey('ctrl+alt+j', 3)  # 连接建立时的快捷键，发送3次
            else:
                # 检测到远程连接断开，发送快捷键
                print(f"[{timestamp}] 检测到远程连接断开")
                send_hotkey('ctrl+alt+h', 5)  # 连接断开时的快捷键，发送5次

        # 更新上一次的远程连接状态（无论是否为None）
        previous_remote_status = current_remote_status

        # 每隔3秒监测1次
        time.sleep(3)